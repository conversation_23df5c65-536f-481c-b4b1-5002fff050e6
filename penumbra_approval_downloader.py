import os
import requests
import json
from urllib.parse import urljoin
from datetime import datetime

class PenumbraApprovalDownloader:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.parallel.ai/v1beta/search"
        self.output_dir = f"penumbra_approvals_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.session = requests.Session()
        self.session.headers.update({
            'x-api-key': self.api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0'
        })
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)

    def search_approvals(self):
        """Search for Penumbra FDA approvals"""
        search_queries = [
            "Penumbra pre-market approval site:accessdata.fda.gov",
            "Penumbra PMA documents FDA",
            "Penumbra 510(k) clearances FDA database"
        ]
        
        all_results = []
        for query in search_queries:
            try:
                response = self.session.post(
                    self.base_url,
                    json={
                        "search_queries": [query],
                        "processor": "base",
                        "max_results": 10,
                        "max_chars_per_result": 1000
                    },
                    timeout=30
                )
                response.raise_for_status()
                results = response.json().get('results', [])
                all_results.extend(results)
                print(f"Found {len(results)} results for: {query}")
            except Exception as e:
                print(f"Error searching for '{query}': {str(e)}")
        
        return all_results

    def download_pdf(self, url, index):
        """Download a PDF file from URL"""
        try:
            # Clean up the filename
            filename = f"penumbra_approval_{index}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"Downloaded: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error downloading {url}: {str(e)}")
            return None

    def run(self):
        print("Starting Penumbra FDA approval downloader...")
        print(f"Results will be saved to: {os.path.abspath(self.output_dir)}")
        
        # Search for approvals
        print("\nSearching for Penumbra FDA approvals...")
        results = self.search_approvals()
        
        if not results:
            print("No results found.")
            return
        
        # Download PDFs
        print(f"\nFound {len(results)} results. Downloading PDFs...")
        for i, result in enumerate(results, 1):
            url = result.get('url', '')
            if url and url.lower().endswith('.pdf'):
                print(f"\nProcessing document {i}: {result.get('title', 'Untitled')}")
                print(f"URL: {url}")
                self.download_pdf(url, i)
            else:
                print(f"\nSkipping non-PDF URL: {url}")

        print("\nDownload complete!")

if __name__ == "__main__":
    # Replace with your actual Parallel.ai API key
    API_KEY = "lyUr36G-npvAY-z_KSmUgDKbFWuCHDjD_5qm9L82"
    
    if not API_KEY or API_KEY == "your_parallel_api_key_here":
        print("Please set your Parallel.ai API key in the script")
    else:
        downloader = PenumbraApprovalDownloader(API_KEY)
        downloader.run()