"""
Requires:
    pip install -r requirements.txt
"""
import requests
import json
import math

# Base URL and search parameters
base_url = "https://api.fda.gov/device/510k.json"
search_query = 'applicant.exact:"PENUMBRA, INC."'
limit = 100  # Max per page

# Step 1: Get total number of results
params = {
    'search': search_query,
    'limit': 1
}
response = requests.get(base_url, params=params)
if response.status_code != 200:
    raise Exception(f"API request failed: {response.status_code}")
data = response.json()
total_results = data['meta']['results']['total']
print(f"Total Penumbra 510(k) notifications: {total_results}")

# Step 2: Paginate and fetch all results
all_results = []
for skip in range(0, total_results, limit):
    params = {
        'search': search_query,
        'limit': limit,
        'skip': skip
    }
    response = requests.get(base_url, params=params)
    if response.status_code != 200:
        raise Exception(f"API request failed at skip {skip}: {response.status_code}")
    page_data = response.json()
    all_results.extend(page_data['results'])
    print(f"Fetched {len(page_data['results'])} results (skip: {skip})")

# Step 3: Save to file (or process further, e.g., to CSV)
with open('penumbra_510k_results.json', 'w') as f:
    json.dump(all_results, f, indent=4)

print(f"Extracted {len(all_results)} results and saved to penumbra_510k_results.json")